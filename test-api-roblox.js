const axios = require('axios');

// Test the new Roblox-safe API endpoint
const testRobloxSafeAPI = async () => {
    const problematicCode = `
-- Code that typically causes CurrentCamera errors
local Players = game:GetService("Players")
local player = Players.LocalPlayer

-- This line causes the error you're seeing
local camera = workspace.CurrentCamera
camera.CameraType = Enum.CameraType.Custom

-- More operations that can fail
local humanoid = player.Character:WaitFor<PERSON>hild("Humanoid")
camera.CameraSubject = humanoid

print("Camera setup complete!")
`;

    try {
        console.log('🚀 Testing Roblox-Safe API Endpoint...\n');
        
        const response = await axios.post('http://localhost:3000/api/obfuscate-roblox', {
            code: problematicCode
        });

        if (response.data.success) {
            console.log('✅ SUCCESS! Roblox-safe obfuscation completed');
            console.log('📊 Stats:');
            console.log(`   Original size: ${response.data.originalSize} characters`);
            console.log(`   Obfuscated size: ${response.data.obfuscatedSize} characters`);
            console.log(`   Features: ${response.data.features.join(', ')}`);
            
            console.log('\n🔒 OBFUSCATED CODE (Ready for Roblox):');
            console.log('='.repeat(80));
            console.log(response.data.obfuscated);
            console.log('='.repeat(80));
            
            console.log('\n📋 INSTRUCTIONS:');
            console.log('1. Copy the obfuscated code above');
            console.log('2. Create a LocalScript in Roblox Studio');
            console.log('3. Place it in StarterPlayer > StarterPlayerScripts');
            console.log('4. Run your game - CurrentCamera errors should be eliminated!');
            
        } else {
            console.error('❌ API Error:', response.data.error);
        }
        
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.error('❌ Server not running! Start it with: node server.js');
        } else {
            console.error('❌ Request failed:', error.message);
        }
    }
};

// Also test the regular API for comparison
const testRegularAPI = async () => {
    const simpleCode = `
local x = 10
local y = 20
print("Sum:", x + y)
`;

    try {
        console.log('\n🔧 Testing Regular API Endpoint...\n');
        
        const response = await axios.post('http://localhost:3000/api/obfuscate', {
            code: simpleCode,
            options: { robloxSafe: false }
        });

        if (response.data.success) {
            console.log('✅ Regular obfuscation completed');
            console.log('📊 Stats:');
            console.log(`   Original size: ${response.data.originalSize} characters`);
            console.log(`   Obfuscated size: ${response.data.obfuscatedSize} characters`);
            console.log(`   Features: ${response.data.features.join(', ')}`);
            
            console.log('\n🔒 REGULAR OBFUSCATED CODE:');
            console.log('='.repeat(50));
            console.log(response.data.obfuscated);
            console.log('='.repeat(50));
            
        } else {
            console.error('❌ API Error:', response.data.error);
        }
        
    } catch (error) {
        console.error('❌ Request failed:', error.message);
    }
};

// Run both tests
const runTests = async () => {
    await testRobloxSafeAPI();
    await testRegularAPI();
    
    console.log('\n🎯 SUMMARY:');
    console.log('- Use /api/obfuscate-roblox for Roblox scripts with CurrentCamera issues');
    console.log('- Use /api/obfuscate for regular Lua code obfuscation');
    console.log('- Both endpoints provide heavy obfuscation with Base64 + XOR + VM dispatcher');
};

runTests();
