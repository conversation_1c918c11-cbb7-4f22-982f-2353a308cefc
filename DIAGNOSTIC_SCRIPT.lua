-- DIAGNOSTIC SCRIPT FOR CURRENTCAMERA ISSUES
-- Run this first to understand what's happening in your Roblox environment
-- Place in a LocalScript in StarterPlayer > StarterPlayerScripts

print("🔍 ROBLOX CURRENTCAMERA DIAGNOSTIC STARTING...")
print("=" .. string.rep("=", 60))

-- Function to safely print values
local function safePrint(label, value)
    local success, result = pcall(function()
        return tostring(value)
    end)
    if success then
        print(label .. ": " .. result)
    else
        print(label .. ": ERROR - " .. tostring(result))
    end
end

-- Function to wait and report
local function waitAndReport(condition, description, maxWait)
    print("⏳ Waiting for: " .. description)
    local startTime = tick()
    local attempts = 0
    
    while not condition() and (tick() - startTime) < maxWait do
        wait(0.1)
        attempts = attempts + 1
        if attempts % 10 == 0 then
            print("   Still waiting... (" .. math.floor(tick() - startTime) .. "s)")
        end
    end
    
    local elapsed = tick() - startTime
    if condition() then
        print("✅ " .. description .. " (took " .. string.format("%.1f", elapsed) .. "s)")
        return true
    else
        print("❌ " .. description .. " FAILED after " .. string.format("%.1f", elapsed) .. "s")
        return false
    end
end

-- Start diagnostics
print("\n📊 INITIAL STATE:")
safePrint("game exists", game ~= nil)
safePrint("game:IsLoaded()", game and game:IsLoaded())
safePrint("workspace exists", workspace ~= nil)
safePrint("workspace.Parent", workspace and workspace.Parent)

-- Wait for game to load
print("\n🎮 GAME LOADING:")
waitAndReport(function() return game:IsLoaded() end, "game:IsLoaded()", 30)

-- Check Players service
print("\n👥 PLAYERS SERVICE:")
local Players = game:GetService("Players")
safePrint("Players service", Players)
safePrint("LocalPlayer", Players and Players.LocalPlayer)

-- Wait for LocalPlayer
if Players then
    waitAndReport(function() return Players.LocalPlayer ~= nil end, "LocalPlayer availability", 10)
end

-- Check workspace thoroughly
print("\n🌍 WORKSPACE ANALYSIS:")
safePrint("workspace", workspace)
safePrint("workspace.Parent", workspace and workspace.Parent)
safePrint("workspace.Name", workspace and workspace.Name)

if workspace then
    safePrint("workspace children count", #workspace:GetChildren())
    print("📋 Workspace children:")
    for i, child in ipairs(workspace:GetChildren()) do
        if i <= 10 then -- Limit to first 10
            safePrint("   [" .. i .. "]", child.Name .. " (" .. child.ClassName .. ")")
        end
    end
    if #workspace:GetChildren() > 10 then
        print("   ... and " .. (#workspace:GetChildren() - 10) .. " more")
    end
end

-- CurrentCamera analysis
print("\n📷 CURRENTCAMERA ANALYSIS:")
safePrint("workspace.CurrentCamera", workspace and workspace.CurrentCamera)

if workspace then
    -- Try different methods to find camera
    local methods = {
        {name = "Direct access", func = function() return workspace.CurrentCamera end},
        {name = "FindFirstChild('CurrentCamera')", func = function() return workspace:FindFirstChild("CurrentCamera") end},
        {name = "FindFirstChild('Camera')", func = function() return workspace:FindFirstChild("Camera") end},
        {name = "FindFirstChildOfClass('Camera')", func = function() return workspace:FindFirstChildOfClass("Camera") end},
    }
    
    for _, method in ipairs(methods) do
        local success, result = pcall(method.func)
        if success and result then
            print("✅ " .. method.name .. ": " .. tostring(result))
        else
            print("❌ " .. method.name .. ": " .. (success and "nil" or tostring(result)))
        end
    end
    
    -- Try WaitForChild
    print("\n⏳ Testing WaitForChild methods:")
    local waitMethods = {
        {name = "WaitForChild('CurrentCamera', 5)", func = function() return workspace:WaitForChild("CurrentCamera", 5) end},
        {name = "WaitForChild('Camera', 5)", func = function() return workspace:WaitForChild("Camera", 5) end},
    }
    
    for _, method in ipairs(waitMethods) do
        local startTime = tick()
        local success, result = pcall(method.func)
        local elapsed = tick() - startTime
        
        if success and result then
            print("✅ " .. method.name .. ": " .. tostring(result) .. " (took " .. string.format("%.1f", elapsed) .. "s)")
        else
            print("❌ " .. method.name .. ": " .. (success and "timeout" or tostring(result)) .. " (took " .. string.format("%.1f", elapsed) .. "s)")
        end
    end
end

-- Character analysis
print("\n🚶 CHARACTER ANALYSIS:")
if Players and Players.LocalPlayer then
    local player = Players.LocalPlayer
    safePrint("player.Character", player.Character)
    
    if not player.Character then
        print("⏳ Waiting for character...")
        waitAndReport(function() return player.Character ~= nil end, "Character spawn", 30)
    end
    
    if player.Character then
        safePrint("Character.Parent", player.Character.Parent)
        safePrint("Character children count", #player.Character:GetChildren())
    end
end

-- RunService analysis
print("\n⚡ RUNSERVICE ANALYSIS:")
local RunService = game:GetService("RunService")
safePrint("RunService", RunService)
safePrint("RunService.Heartbeat", RunService and RunService.Heartbeat)

-- Final recommendations
print("\n💡 RECOMMENDATIONS:")
if not game:IsLoaded() then
    print("❗ Game is not fully loaded - add 'repeat wait() until game:IsLoaded()' at the start")
end

if not workspace then
    print("❗ Workspace is nil - this is a critical issue")
elseif not workspace.Parent then
    print("❗ Workspace.Parent is nil - workspace may not be properly initialized")
end

if workspace and not workspace.CurrentCamera then
    print("❗ CurrentCamera is nil - try using WaitForChild or the ultimate fix script")
    print("   Recommended: Use the ULTIMATE_OBFUSCATED_FIX.lua script")
end

if Players and not Players.LocalPlayer then
    print("❗ LocalPlayer is nil - script may be running too early or in wrong context")
    print("   Make sure this is a LocalScript in StarterPlayerScripts")
end

print("\n🎯 SCRIPT TYPE CHECK:")
if game:GetService("RunService"):IsServer() then
    print("❌ This script is running on the SERVER - it should be a LocalScript!")
    print("   Move this to a LocalScript in StarterPlayer > StarterPlayerScripts")
elseif game:GetService("RunService"):IsClient() then
    print("✅ This script is running on the CLIENT (correct)")
else
    print("❓ Unknown script context")
end

print("\n" .. string.rep("=", 60))
print("🔍 DIAGNOSTIC COMPLETE")
print("Copy this output and use it to understand what's happening in your environment.")
