const luamin = require('./luamin.js');

// Test the problematic code that causes CurrentCamera errors
const problematicRobloxCode = `
-- This is the type of code that causes CurrentCamera errors
local Players = game:GetService("Players")
local player = Players.LocalPlayer

-- This line often fails with "attempt to index nil with 'CurrentCamera'"
local camera = workspace.CurrentCamera
camera.CameraType = Enum.CameraType.Custom

-- More camera operations that can fail
local cf = camera.CFrame
camera.CFrame = cf * CFrame.new(0, 5, 0)

print("Camera setup complete")
`;

console.log('🔧 TESTING ROBLOX-SAFE OBFUSCATION');
console.log('='.repeat(80));
console.log('\n📋 Original problematic code:');
console.log(problematicRobloxCode);

console.log('\n🚀 Creating Roblox-safe obfuscated version...\n');

try {
    // Create the safety wrapper manually for testing
    const safetyWrapper = `
-- Roblox Safety Wrapper (Auto-generated)
local RunService = game:GetService("RunService")
local Players = game:GetService("Players")

-- Safe workspace checker
local function waitForWorkspace()
    local attempts = 0
    while not workspace and attempts < 100 do
        if RunService and RunService.Heartbeat then
            RunService.Heartbeat:Wait()
        else
            wait(0.1)
        end
        attempts = attempts + 1
    end
    return workspace ~= nil
end

-- Ultra-safe CurrentCamera getter
local function getSafeCurrentCamera()
    -- Ensure workspace exists first
    if not waitForWorkspace() then
        warn("Workspace failed to load")
        return nil
    end
    
    -- Method 1: Direct check
    if workspace.CurrentCamera then
        return workspace.CurrentCamera
    end
    
    -- Method 2: Wait with heartbeat
    local attempts = 0
    while attempts < 100 do
        if workspace.CurrentCamera then
            return workspace.CurrentCamera
        end
        if RunService and RunService.Heartbeat then
            RunService.Heartbeat:Wait()
        else
            wait(0.05)
        end
        attempts = attempts + 1
    end
    
    -- Method 3: Try FindFirstChild
    local success, camera = pcall(function()
        return workspace:FindFirstChild("Camera") or workspace:FindFirstChild("CurrentCamera")
    end)
    if success and camera then
        return camera
    end
    
    -- Method 4: Force wait and retry
    wait(2)
    return workspace.CurrentCamera
end

-- Replace all workspace.CurrentCamera references
local OriginalWorkspace = workspace
local SafeWorkspace = setmetatable({}, {
    __index = function(t, k)
        if k == "CurrentCamera" then
            return getSafeCurrentCamera()
        else
            return OriginalWorkspace[k]
        end
    end,
    __newindex = function(t, k, v)
        OriginalWorkspace[k] = v
    end
})

-- Override workspace reference
workspace = SafeWorkspace

-- Execute original code safely
local function executeOriginalCode()
${problematicRobloxCode.split('\n').map(line => '    ' + line).join('\n')}
end

-- Run with error handling
local success, errorMsg = pcall(executeOriginalCode)
if not success then
    warn("Script execution failed: " .. tostring(errorMsg))
end
`;

    // Obfuscate the safe version
    const obfuscated = luamin.minify(safetyWrapper);
    
    console.log('✅ ROBLOX-SAFE OBFUSCATED CODE:');
    console.log('='.repeat(80));
    console.log(obfuscated);
    console.log('='.repeat(80));
    
    console.log('\n🛡️  SAFETY FEATURES INCLUDED:');
    console.log('✓ Workspace existence verification');
    console.log('✓ CurrentCamera safety wrapper');
    console.log('✓ Multiple fallback methods for camera access');
    console.log('✓ RunService.Heartbeat timing');
    console.log('✓ Comprehensive error handling');
    console.log('✓ Metatable-based workspace override');
    
    console.log('\n📋 USAGE INSTRUCTIONS:');
    console.log('1. Copy the obfuscated code above');
    console.log('2. Create a LocalScript in Roblox Studio');
    console.log('3. Place in StarterPlayer > StarterPlayerScripts');
    console.log('4. The CurrentCamera error should be completely eliminated');
    
    console.log('\n⚡ HOW IT WORKS:');
    console.log('- Intercepts all workspace.CurrentCamera access');
    console.log('- Replaces with safe getter function');
    console.log('- Waits for proper initialization before proceeding');
    console.log('- Uses multiple fallback strategies');
    console.log('- Wraps everything in pcall for error safety');
    
} catch (error) {
    console.error('❌ ERROR:', error.message);
    console.error('Stack:', error.stack);
}
