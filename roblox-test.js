const luamin = require('./luamin.js');

// Test with Roblox-specific code patterns
const robloxCode = `
-- Roblox script example
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

local function createGui()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MyGui"
    screenGui.Parent = playerGui
    
    local frame = Instance.new("Frame")
    frame.Size = UDim2.new(0.5, 0, 0.5, 0)
    frame.Position = UDim2.new(0.25, 0, 0.25, 0)
    frame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    frame.Parent = screenGui
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.Text = "Hello, <PERSON>lo<PERSON>!"
    textLabel.TextColor3 = Color3.new(1, 1, 1)
    textLabel.BackgroundTransparency = 1
    textLabel.Parent = frame
    
    return screenGui
end

local function onPlayerAdded(newPlayer)
    print("Player joined: " .. newPlayer.Name)
    
    newPlayer.CharacterAdded:Connect(function(character)
        local humanoid = character:WaitForChild("Humanoid")
        humanoid.WalkSpeed = 20
        
        print(newPlayer.Name .. " spawned with character: " .. character.Name)
    end)
end

-- Connect events
Players.PlayerAdded:Connect(onPlayerAdded)

-- Handle existing players
for _, existingPlayer in pairs(Players:GetPlayers()) do
    onPlayerAdded(existingPlayer)
end

-- Create the GUI
local myGui = createGui()

-- Example of table manipulation
local inventory = {
    coins = 100,
    items = {"Sword", "Shield", "Potion"},
    equipped = {
        weapon = "Sword",
        armor = "Shield"
    }
}

for itemName, itemData in pairs(inventory.equipped) do
    print("Equipped " .. itemName .. ": " .. itemData)
end

-- Math operations
local damage = math.random(10, 20)
local criticalHit = damage * 1.5
local finalDamage = math.floor(criticalHit)

print("Final damage: " .. finalDamage)
`;

console.log('Testing Roblox Lua 5.1 compatibility...\n');
console.log('Original Roblox code:');
console.log(robloxCode);
console.log('\n' + '='.repeat(80) + '\n');

try {
    const obfuscated = luamin.minify(robloxCode);
    console.log('Obfuscated Roblox-compatible code:');
    console.log(obfuscated);
    
    // Check code length reduction
    const originalLength = robloxCode.length;
    const obfuscatedLength = obfuscated.length;
    const compressionRatio = ((originalLength - obfuscatedLength) / originalLength * 100).toFixed(1);
    
    console.log('\n' + '='.repeat(80));
    console.log('OBFUSCATION SUMMARY:');
    console.log('='.repeat(80));
    console.log(`✅ Successfully obfuscated for Lua 5.1 (Roblox)`);
    console.log(`📊 Original size: ${originalLength} characters`);
    console.log(`📊 Obfuscated size: ${obfuscatedLength} characters`);
    console.log(`📈 Size change: ${compressionRatio > 0 ? '-' : '+'}${Math.abs(compressionRatio)}%`);
    console.log(`🔒 Features applied:`);
    console.log(`   • Random identifier names`);
    console.log(`   • Base64 string encoding with noise`);
    console.log(`   • Function call dispatcher`);
    console.log(`   • Lua 5.1 compatible bitwise operations`);
    console.log(`   • No Lua 5.2+ features used`);
    
} catch (error) {
    console.error('❌ ERROR during obfuscation:', error.message);
    console.error('Stack trace:', error.stack);
}
