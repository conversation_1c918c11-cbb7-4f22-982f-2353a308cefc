-- ULTIMATE CURRENTCAMERA FIX - Most Aggressive Approach
-- This script uses the most aggressive timing and safety measures possible
-- Place this in a LocalScript in StarterPlayer > StarterPlayerScripts

-- Wait for game to be fully loaded first
repeat wait() until game:IsLoaded()

-- Additional safety: wait for workspace to be fully initialized
repeat wait() until workspace
repeat wait() until workspace.Parent

-- Wait for Players service and LocalPlayer
local Players = game:GetService("Players")
repeat wait() until Players
repeat wait() until Players.LocalPlayer

local player = Players.LocalPlayer
local RunService = game:GetService("RunService")

-- Ultra-aggressive CurrentCamera getter
local function getUltraSafeCurrentCamera()
    -- Step 1: Wait for workspace to exist and be stable
    local attempts = 0
    while (not workspace or not workspace.Parent) and attempts < 200 do
        wait(0.1)
        attempts = attempts + 1
    end
    
    if not workspace then
        error("Workspace never loaded")
        return nil
    end
    
    -- Step 2: Try multiple methods with extensive waiting
    local methods = {
        -- Method 1: Direct access
        function()
            return workspace.CurrentCamera
        end,
        
        -- Method 2: FindFirstChild
        function()
            return workspace:FindFirstChild("CurrentCamera")
        end,
        
        -- Method 3: FindFirstChild with "Camera"
        function()
            return workspace:FindFirstChild("Camera")
        end,
        
        -- Method 4: WaitForChild (most aggressive)
        function()
            return workspace:WaitForChild("CurrentCamera", 10)
        end,
        
        -- Method 5: Create if doesn't exist (last resort)
        function()
            local camera = workspace:FindFirstChild("CurrentCamera")
            if not camera then
                -- This shouldn't normally be needed, but just in case
                wait(5)
                camera = workspace:FindFirstChild("CurrentCamera")
            end
            return camera
        end
    }
    
    -- Try each method with retries
    for methodIndex, method in ipairs(methods) do
        for retry = 1, 10 do
            local success, camera = pcall(method)
            if success and camera then
                print("✅ CurrentCamera found using method " .. methodIndex .. " (retry " .. retry .. ")")
                return camera
            end
            
            -- Wait between retries, increasing wait time
            wait(retry * 0.5)
        end
    end
    
    error("Failed to get CurrentCamera after all methods and retries")
    return nil
end

-- Create a completely safe workspace wrapper
local originalWorkspace = workspace
local safeWorkspaceMetatable = {
    __index = function(t, key)
        if key == "CurrentCamera" then
            local camera = getUltraSafeCurrentCamera()
            if camera then
                return camera
            else
                error("CurrentCamera is nil even after ultra-safe getter")
            end
        else
            return originalWorkspace[key]
        end
    end,
    
    __newindex = function(t, key, value)
        originalWorkspace[key] = value
    end
}

-- Replace workspace globally
workspace = setmetatable({}, safeWorkspaceMetatable)

-- Also create a backup reference
_G.SafeWorkspace = workspace

-- Your actual script code goes here
local function executeMainScript()
    print("🚀 Starting ultra-safe script execution...")
    
    -- Wait for character with maximum safety
    local character = player.Character
    if not character then
        print("⏳ Waiting for character...")
        character = player.CharacterAdded:Wait()
    end
    
    -- Additional character safety
    repeat wait() until character.Parent
    print("✅ Character loaded and parented")
    
    -- Now safely access CurrentCamera
    print("🎥 Accessing CurrentCamera...")
    local camera = workspace.CurrentCamera
    
    if not camera then
        error("CurrentCamera is still nil after all safety measures!")
        return
    end
    
    print("✅ CurrentCamera obtained: " .. tostring(camera))
    
    -- Safe camera operations
    local success, err = pcall(function()
        -- Basic camera setup
        camera.CameraType = Enum.CameraType.Custom
        
        -- Wait for humanoid safely
        local humanoid = character:WaitForChild("Humanoid", 30)
        if humanoid then
            camera.CameraSubject = humanoid
            print("✅ Camera subject set to humanoid")
        end
        
        -- Example camera manipulation
        local originalCFrame = camera.CFrame
        camera.CFrame = originalCFrame * CFrame.new(0, 2, 0)
        
        print("✅ Camera positioned successfully")
        
        -- Test camera properties
        print("📊 Camera Info:")
        print("   CameraType: " .. tostring(camera.CameraType))
        print("   CameraSubject: " .. tostring(camera.CameraSubject))
        print("   CFrame: " .. tostring(camera.CFrame))
        
        return true
    end)
    
    if success then
        print("🎉 Script completed successfully!")
    else
        warn("⚠️ Script execution failed: " .. tostring(err))
    end
end

-- Execute with maximum error handling
print("🛡️ Initializing ultimate CurrentCamera safety system...")

-- Wait a bit more to ensure everything is loaded
wait(2)

local success, errorMsg = pcall(executeMainScript)
if not success then
    warn("❌ Ultimate script failed: " .. tostring(errorMsg))
    warn("💡 This suggests a deeper issue with your Roblox setup")
    
    -- Final diagnostic
    print("🔍 Diagnostic Information:")
    print("   game:IsLoaded(): " .. tostring(game:IsLoaded()))
    print("   workspace exists: " .. tostring(workspace ~= nil))
    print("   workspace.Parent: " .. tostring(workspace and workspace.Parent))
    print("   Players.LocalPlayer: " .. tostring(Players.LocalPlayer))
    
    if workspace then
        print("   workspace.CurrentCamera: " .. tostring(workspace.CurrentCamera))
        print("   workspace children count: " .. tostring(#workspace:GetChildren()))
    end
else
    print("✅ Ultimate script execution completed!")
end

-- Monitor for any workspace issues during runtime
RunService.Heartbeat:Connect(function()
    if not workspace then
        warn("⚠️ Workspace became nil during runtime!")
    elseif not workspace.CurrentCamera then
        warn("⚠️ CurrentCamera became nil during runtime!")
    end
end)
