* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
    min-height: 100vh;
    color: #e2e8f0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: #f1f5f9;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

header p {
    font-size: 1.1rem;
    opacity: 0.8;
    color: #cbd5e1;
}

.editor-section {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.4);
    margin-bottom: 30px;
}

.input-panel, .output-panel {
    margin-bottom: 20px;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #334155;
}

.panel-header h3 {
    color: #f1f5f9;
    font-size: 1.3rem;
}

.panel-actions {
    display: flex;
    gap: 10px;
}

textarea {
    width: 100%;
    min-height: 200px;
    padding: 15px;
    border: 2px solid #475569;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.3s ease;
    background-color: #0f172a;
    color: #e2e8f0;
}

textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

textarea[readonly] {
    background-color: #1e293b;
    cursor: default;
    border-color: #374151;
}

.input-info, .output-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 0.9rem;
    color: #94a3b8;
}

.controls {
    text-align: center;
    margin: 30px 0;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    font-size: 1.1rem;
    padding: 15px 30px;
    border: 1px solid #4c1d95;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
}

.btn-secondary {
    background: #475569;
    color: #f1f5f9;
    border: 1px solid #64748b;
}

.btn-secondary:hover:not(:disabled) {
    background: #374151;
}

.btn-success {
    background: #059669;
    color: white;
    border: 1px solid #047857;
}

.btn-success:hover:not(:disabled) {
    background: #047857;
}

.features-section {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.4);
    margin-bottom: 30px;
}

.features-section h3 {
    text-align: center;
    margin-bottom: 25px;
    color: #f1f5f9;
    font-size: 1.5rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.feature-card {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    background: #0f172a;
    border: 1px solid #374151;
    transition: transform 0.3s ease, border-color 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    border-color: #3b82f6;
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.2);
}

.feature-icon {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #3b82f6;
}

.feature-card h4 {
    margin-bottom: 10px;
    color: #f1f5f9;
}

.feature-card p {
    color: #94a3b8;
    font-size: 0.9rem;
}

.warning-section {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.4);
    margin-bottom: 30px;
}

.warning-box {
    background: #451a03;
    border: 1px solid #ea580c;
    border-radius: 8px;
    padding: 20px;
}

.warning-box h4 {
    color: #fb923c;
    margin-bottom: 15px;
}

.warning-box ul {
    color: #fdba74;
    padding-left: 20px;
}

.warning-box li {
    margin-bottom: 5px;
}

footer {
    text-align: center;
    color: #94a3b8;
    opacity: 0.8;
    padding: 20px 0;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 600;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
    border: 1px solid;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: #059669;
    border-color: #047857;
}

.notification.error {
    background: #dc2626;
    border-color: #b91c1c;
}

.notification.info {
    background: #0284c7;
    border-color: #0369a1;
}

/* Dark theme scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1e293b;
}

::-webkit-scrollbar-thumb {
    background: #475569;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #64748b;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .editor-section {
        padding: 20px;
    }
    
    .panel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .panel-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}