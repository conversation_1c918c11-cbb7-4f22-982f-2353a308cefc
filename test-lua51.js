const luamin = require('./luamin.js');

// Test Lua 5.1 compatibility
const testCode = `
local function greet(name)
    print("Hello, " .. name .. "!")
    return "Greeting sent"
end

local message = "World"
local result = greet(message)

for i = 1, 5 do
    print("Count: " .. i)
end

local numbers = {1, 2, 3, 4, 5}
for k, v in pairs(numbers) do
    print("Index: " .. k .. ", Value: " .. v)
end
`;

console.log('Original Lua code:');
console.log(testCode);
console.log('\n' + '='.repeat(50) + '\n');

try {
    const obfuscated = luamin.minify(testCode);
    console.log('Obfuscated Lua 5.1 compatible code:');
    console.log(obfuscated);
    console.log('\nObfuscation successful! The code is now Lua 5.1 (Roblox) compatible.');
} catch (error) {
    console.error('Error during obfuscation:', error.message);
    console.error('Stack trace:', error.stack);
}
