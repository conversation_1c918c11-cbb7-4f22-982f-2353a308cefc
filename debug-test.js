const luamin = require('./luamin.js');

// Simple test to debug the dispatcher issue
const simpleCode = `
local camera = workspace.CurrentCamera
print("Camera: " .. tostring(camera))
`;

console.log('Testing simple camera access...\n');
console.log('Original code:');
console.log(simpleCode);
console.log('\n' + '='.repeat(50) + '\n');

try {
    const obfuscated = luamin.minify(simpleCode);
    console.log('Obfuscated code:');
    console.log(obfuscated);
    
    // Let's also test if the dispatcher is working correctly
    console.log('\n' + '='.repeat(50));
    console.log('ANALYSIS:');
    console.log('='.repeat(50));
    
    // Check if dispatcher calls are using correct IDs
    const dispatcherMatches = obfuscated.match(/\w+\(\d+,/g);
    if (dispatcherMatches) {
        console.log('Dispatcher calls found:');
        dispatcherMatches.forEach(match => {
            console.log('  ' + match);
        });
    } else {
        console.log('No dispatcher calls found');
    }
    
} catch (error) {
    console.error('Error during obfuscation:', error.message);
    console.error('Stack trace:', error.stack);
}
