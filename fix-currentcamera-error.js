const luamin = require('./luamin.js');

// Specific fix for the CurrentCamera nil error
const fixedRobloxCode = `
-- Fixed Roblox script that prevents CurrentCamera nil errors
local RunService = game:GetService("RunService")
local Players = game:GetService("Players")

local player = Players.LocalPlayer

-- Wait for workspace to be fully loaded
local function waitForWorkspace()
    local attempts = 0
    while not workspace and attempts < 100 do
        RunService.Heartbeat:Wait()
        attempts = attempts + 1
    end
    return workspace ~= nil
end

-- Safe camera getter with multiple fallback methods
local function getCurrentCameraSafe()
    -- Method 1: Direct access with nil check
    if workspace and workspace.CurrentCamera then
        return workspace.CurrentCamera
    end
    
    -- Method 2: Wait for CurrentCamera to exist
    local attempts = 0
    while attempts < 50 do
        if workspace and workspace.CurrentCamera then
            return workspace.CurrentCamera
        end
        RunService.Heartbeat:Wait()
        attempts = attempts + 1
    end
    
    -- Method 3: Try to create/find camera through other means
    local success, camera = pcall(function()
        return workspace:Find<PERSON>irstChild("Camera") or workspace.CurrentCamera
    end)
    
    if success and camera then
        return camera
    end
    
    -- Method 4: Last resort - wait longer
    wait(1)
    if workspace and workspace.CurrentCamera then
        return workspace.CurrentCamera
    end
    
    return nil
end

-- Main execution with comprehensive error handling
local function main()
    print("Starting Roblox script with CurrentCamera fix...")
    
    -- Ensure workspace is loaded
    if not waitForWorkspace() then
        error("Workspace failed to load")
        return
    end
    
    print("Workspace loaded successfully")
    
    -- Wait for player character
    if not player then
        warn("LocalPlayer not available")
        return
    end
    
    local character = player.Character or player.CharacterAdded:Wait()
    print("Character loaded: " .. tostring(character))
    
    -- Get camera safely
    local camera = getCurrentCameraSafe()
    if not camera then
        error("Failed to get CurrentCamera after all attempts")
        return
    end
    
    print("CurrentCamera obtained successfully: " .. tostring(camera))
    
    -- Safe camera operations
    local success, err = pcall(function()
        camera.CameraType = Enum.CameraType.Custom
        camera.CameraSubject = character:FindFirstChild("Humanoid")
        
        -- Example camera manipulation
        local cf = camera.CFrame
        print("Camera CFrame: " .. tostring(cf))
        
        return true
    end)
    
    if success then
        print("Camera configured successfully!")
    else
        warn("Camera configuration failed: " .. tostring(err))
    end
    
    -- Additional safe operations
    local inventory = {
        gold = 500,
        weapons = {"Laser Gun", "Plasma Sword", "Energy Shield"}
    }
    
    print("Inventory loaded with " .. inventory.gold .. " gold")
    for i, weapon in ipairs(inventory.weapons) do
        print("Weapon " .. i .. ": " .. weapon)
    end
    
    print("Script execution completed without errors!")
end

-- Execute main function with error handling
local success, error_msg = pcall(main)
if not success then
    warn("Script failed with error: " .. tostring(error_msg))
end
`;

console.log('Creating obfuscated version that fixes CurrentCamera errors...\n');

try {
    const obfuscated = luamin.minify(fixedRobloxCode);
    
    console.log('✅ OBFUSCATED ROBLOX SCRIPT (CurrentCamera Error Fix):');
    console.log('='.repeat(80));
    console.log(obfuscated);
    console.log('='.repeat(80));
    
    console.log('\n🔧 WHAT THIS FIX DOES:');
    console.log('1. Waits for workspace to be fully loaded before accessing CurrentCamera');
    console.log('2. Uses RunService.Heartbeat:Wait() instead of wait() for better timing');
    console.log('3. Has 4 different fallback methods to get the camera');
    console.log('4. Uses comprehensive pcall error handling');
    console.log('5. Waits for character properly using CharacterAdded event');
    
    console.log('\n📋 HOW TO USE:');
    console.log('1. Copy the obfuscated code above');
    console.log('2. Paste it into a LocalScript in Roblox Studio');
    console.log('3. Place the LocalScript in StarterPlayer > StarterPlayerScripts');
    console.log('4. Test in-game - the CurrentCamera error should be resolved');
    
    console.log('\n⚠️  IF ERRORS STILL OCCUR:');
    console.log('- Make sure you\'re using a LocalScript, not a regular Script');
    console.log('- Try placing the script in StarterGui instead');
    console.log('- Add more wait time before accessing workspace');
    console.log('- Check if your game has custom camera systems that interfere');
    
} catch (error) {
    console.error('❌ ERROR:', error.message);
}
