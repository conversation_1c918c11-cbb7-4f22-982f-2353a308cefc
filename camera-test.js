const luamin = require('./luamin.js');

// Test code that might cause the CurrentCamera error
const cameraCode = `
-- This is the type of code that might cause the CurrentCamera error
local Players = game:GetService("Players")
local player = Players.LocalPlayer

-- Wait for character to load
if player.Character then
    local camera = workspace.CurrentCamera
    if camera then
        print("Camera found: " .. tostring(camera))
        camera.CameraType = Enum.CameraType.Custom
    else
        print("Camera is nil!")
    end
else
    print("Character not loaded yet")
end

-- Another common pattern that might fail
local function setupCamera()
    local camera = workspace.CurrentCamera
    return camera
end

local myCamera = setupCamera()
if myCamera then
    print("Setup successful")
else
    print("Setup failed - camera is nil")
end
`;

console.log('Testing camera access patterns...\n');
console.log('Original code:');
console.log(cameraCode);
console.log('\n' + '='.repeat(80) + '\n');

try {
    const obfuscated = luamin.minify(cameraCode);
    console.log('Obfuscated code:');
    console.log(obfuscated);
    
    console.log('\n' + '='.repeat(80));
    console.log('ANALYSIS:');
    console.log('='.repeat(80));
    
    // Check for potential issues
    if (obfuscated.includes('workspace.CurrentCamera')) {
        console.log('✓ workspace.CurrentCamera references preserved (good for Roblox)');
    }
    
    // Check dispatcher calls
    const dispatcherMatches = obfuscated.match(/\w+\(\d+,/g);
    if (dispatcherMatches) {
        console.log('✓ Dispatcher calls found:');
        const uniqueCalls = [...new Set(dispatcherMatches)];
        uniqueCalls.forEach(match => {
            console.log('  ' + match);
        });
    }
    
    // Check for any potential nil access patterns
    if (obfuscated.includes('.CurrentCamera')) {
        console.log('⚠️  Direct property access on workspace - ensure workspace is not nil');
    }
    
} catch (error) {
    console.error('❌ Error during obfuscation:', error.message);
    console.error('Stack trace:', error.stack);
}
