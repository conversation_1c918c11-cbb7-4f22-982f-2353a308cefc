const luamin = require('./luamin.js');

// Test various Lua constructs to ensure Lua 5.1 compatibility
const testCodes = [
    {
        name: "Basic function with string operations",
        code: `
local function processText(text)
    local result = "Processed: " .. text
    return result
end

local input = "Hello World"
local output = processText(input)
print(output)
`
    },
    {
        name: "Table operations and loops",
        code: `
local data = {
    name = "Test",
    value = 42,
    items = {1, 2, 3, 4, 5}
}

for key, value in pairs(data) do
    print(key .. ": " .. tostring(value))
end

for i = 1, #data.items do
    print("Item " .. i .. ": " .. data.items[i])
end
`
    },
    {
        name: "Math operations",
        code: `
local function calculate(a, b)
    local sum = a + b
    local product = a * b
    local quotient = a / b
    local remainder = a % b
    local power = a ^ b
    
    return sum, product, quotient, remainder, power
end

local x, y = 10, 3
local results = {calculate(x, y)}
for i, result in ipairs(results) do
    print("Result " .. i .. ": " .. result)
end
`
    }
];

console.log('Testing Lua 5.1 (Roblox) compatibility...\n');

function checkLua51Compatibility(code) {
    // Check for Lua 5.2+ features that should not be present
    const lua52Features = [
        /<<|>>/,  // Bitwise shift operators
        /&(?![&])/,  // Bitwise AND (but not logical &&)
        /\|(?![|])/,  // Bitwise OR (but not logical ||)
        /~/,  // Bitwise XOR
        /::/,  // Labels
        /\bgoto\b/,  // Goto statements
        /\b0x[0-9a-fA-F]*\.[0-9a-fA-F]*\b/,  // Hexadecimal floats
        /\\x[0-9a-fA-F]{2}/,  // Hex escapes in strings
        /\\u\{[0-9a-fA-F]+\}/  // Unicode escapes
    ];
    
    const issues = [];
    lua52Features.forEach((pattern, index) => {
        if (pattern.test(code)) {
            const featureNames = [
                'Bitwise shift operators',
                'Bitwise AND operator',
                'Bitwise OR operator', 
                'Bitwise XOR operator',
                'Label statements',
                'Goto statements',
                'Hexadecimal floats',
                'Hex escapes in strings',
                'Unicode escapes'
            ];
            issues.push(featureNames[index]);
        }
    });
    
    return issues;
}

testCodes.forEach((test, index) => {
    console.log(`Test ${index + 1}: ${test.name}`);
    console.log('Original code:');
    console.log(test.code);
    
    try {
        const obfuscated = luamin.minify(test.code);
        console.log('\nObfuscated code:');
        console.log(obfuscated);
        
        // Check for Lua 5.2+ features
        const issues = checkLua51Compatibility(obfuscated);
        if (issues.length === 0) {
            console.log('✅ PASS: No Lua 5.2+ features detected');
        } else {
            console.log('❌ FAIL: Found Lua 5.2+ features:', issues.join(', '));
        }
        
    } catch (error) {
        console.error('❌ ERROR during obfuscation:', error.message);
    }
    
    console.log('\n' + '='.repeat(80) + '\n');
});

console.log('Lua 5.1 compatibility testing complete!');
