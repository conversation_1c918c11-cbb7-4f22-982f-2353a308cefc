-- ROBLOX CURRENTCAMERA ERROR FIX
-- Copy this entire script into a LocalScript in Roblox Studio
-- Place the LocalScript in StarterPlayer > StarterPlayerScripts

-- This script prevents the "attempt to index nil with 'CurrentCamera'" error
-- by implementing multiple safety checks and fallback methods

local RunService = game:GetService("RunService")
local Players = game:GetService("Players")
local player = Players.LocalPlayer

-- Configuration
local MAX_WORKSPACE_WAIT_ATTEMPTS = 100
local MAX_CAMERA_WAIT_ATTEMPTS = 100
local HEARTBEAT_FALLBACK_WAIT = 0.05
local FINAL_FALLBACK_WAIT = 2

-- Safe workspace checker
local function waitForWorkspace()
    local attempts = 0
    while not workspace and attempts < MAX_WORKSPACE_WAIT_ATTEMPTS do
        if RunService and RunService.Heartbeat then
            RunService.Heartbeat:Wait()
        else
            wait(0.1)
        end
        attempts = attempts + 1
    end
    return workspace ~= nil
end

-- Ultra-safe CurrentC<PERSON>ra getter with multiple fallback methods
local function getSafeCurrentCamera()
    -- Step 1: Ensure workspace exists first
    if not waitForWorkspace() then
        warn("⚠️ Workspace failed to load after maximum attempts")
        return nil
    end
    
    -- Step 2: Direct check (fastest method)
    if workspace.CurrentCamera then
        print("✅ CurrentCamera found via direct access")
        return workspace.CurrentCamera
    end
    
    -- Step 3: Wait with heartbeat timing (more reliable)
    local attempts = 0
    while attempts < MAX_CAMERA_WAIT_ATTEMPTS do
        if workspace.CurrentCamera then
            print("✅ CurrentCamera found after " .. attempts .. " heartbeat attempts")
            return workspace.CurrentCamera
        end
        
        if RunService and RunService.Heartbeat then
            RunService.Heartbeat:Wait()
        else
            wait(HEARTBEAT_FALLBACK_WAIT)
        end
        attempts = attempts + 1
    end
    
    -- Step 4: Try FindFirstChild approach
    local success, camera = pcall(function()
        return workspace:FindFirstChild("Camera") or workspace:FindFirstChild("CurrentCamera")
    end)
    
    if success and camera then
        print("✅ CurrentCamera found via FindFirstChild")
        return camera
    end
    
    -- Step 5: Final fallback - force wait and retry
    print("⏳ Using final fallback method...")
    wait(FINAL_FALLBACK_WAIT)
    
    if workspace.CurrentCamera then
        print("✅ CurrentCamera found via final fallback")
        return workspace.CurrentCamera
    end
    
    -- Step 6: Last resort - return nil with warning
    warn("❌ Failed to get CurrentCamera after all attempts")
    return nil
end

-- Create a safe workspace proxy that intercepts CurrentCamera access
local OriginalWorkspace = workspace
local SafeWorkspace = setmetatable({}, {
    __index = function(t, k)
        if k == "CurrentCamera" then
            return getSafeCurrentCamera()
        else
            return OriginalWorkspace[k]
        end
    end,
    __newindex = function(t, k, v)
        OriginalWorkspace[k] = v
    end
})

-- Override the global workspace reference
workspace = SafeWorkspace

-- Your original code goes here (replace this section with your actual script)
local function executeYourOriginalCode()
    print("🚀 Starting your script with CurrentCamera safety...")
    
    -- Wait for character to load
    local character = player.Character or player.CharacterAdded:Wait()
    print("✅ Character loaded: " .. tostring(character))
    
    -- Now you can safely access workspace.CurrentCamera
    local camera = workspace.CurrentCamera
    if not camera then
        error("Failed to get CurrentCamera even with safety measures")
        return
    end
    
    print("✅ CurrentCamera obtained safely: " .. tostring(camera))
    
    -- Example camera operations (replace with your actual code)
    local success, err = pcall(function()
        camera.CameraType = Enum.CameraType.Custom
        
        -- Wait for humanoid
        local humanoid = character:WaitForChild("Humanoid")
        camera.CameraSubject = humanoid
        
        -- Example camera manipulation
        local originalCFrame = camera.CFrame
        camera.CFrame = originalCFrame * CFrame.new(0, 5, 0)
        
        print("✅ Camera configured successfully!")
        return true
    end)
    
    if success then
        print("🎉 Script completed without errors!")
    else
        warn("⚠️ Camera configuration failed: " .. tostring(err))
    end
end

-- Execute with comprehensive error handling
print("🛡️ Initializing CurrentCamera safety system...")
local success, errorMsg = pcall(executeYourOriginalCode)

if not success then
    warn("❌ Script execution failed: " .. tostring(errorMsg))
    warn("💡 Try placing this script in a different location or check for conflicting scripts")
else
    print("✅ Script execution completed successfully!")
end

-- Additional safety: Monitor for workspace changes
RunService.Heartbeat:Connect(function()
    if not workspace then
        warn("⚠️ Workspace became nil during runtime!")
    end
end)
