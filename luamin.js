/*! https://mths.be/luamin v1.0.4 by @mathias - Modified with Base64 encoding, Dispatcher, and Lua 5.1 (Roblox) compatibility */
;(function(root) {

	// Detect free variables `exports`
	var freeExports = typeof exports == 'object' && exports;

	// Detect free variable `module`
	var freeModule = typeof module == 'object' && module &&
		module.exports == freeExports && module;

	// Detect free variable `global`, from Node.js or Browserified code,
	// and use it as `root`
	var freeGlobal = typeof global == 'object' && global;
	if (freeGlobal.global === freeGlobal || freeGlobal.window === freeGlobal) {
		root = freeGlobal;
	}

	/*--------------------------------------------------------------------------*/

	var luaparse = root.luaparse || require('luaparse');
	luaparse.defaultOptions.comments = false;
	luaparse.defaultOptions.scope = true;
	luaparse.defaultOptions.luaVersion = '5.1'; // Set to Lua 5.1 for Roblox compatibility
	var parse = luaparse.parse;

	var regexAlphaUnderscore = /[a-zA-Z_]/;
	var regexAlphaNumUnderscore = /[a-zA-Z0-9_]/;
	var regexDigits = /[0-9]/;

	// http://www.lua.org/manual/5.2/manual.html#3.4.7
	// http://www.lua.org/source/5.2/lparser.c.html#priority
	var PRECEDENCE = {
		'or': 1,
		'and': 2,
		'<': 3, '>': 3, '<=': 3, '>=': 3, '~=': 3, '==': 3,
		'..': 5,
		'+': 6, '-': 6, // binary -
		'*': 7, '/': 7, '%': 7,
		'unarynot': 8, 'unary#': 8, 'unary-': 8, // unary -
		'^': 10
	};

	// Random identifier generation setup
	var RANDOM_CHARS = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
	var MIN_IDENTIFIER_LENGTH = 8;
	var MAX_IDENTIFIER_LENGTH = 13;

	// Base64 encoding functionality
	var base64Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';

	// Special characters for noise (Lua 5.1 safe - avoiding bitwise operators)
	var noiseChars = '!@#$%^*()_{}[]:;<>,.?`';

	var base64Encode = function(str) {
		var result = '';
		var i = 0;
		while (i < str.length) {
			var a = str.charCodeAt(i++);
			var b = i < str.length ? str.charCodeAt(i++) : 0;
			var c = i < str.length ? str.charCodeAt(i++) : 0;

			var bitmap = (a << 16) | (b << 8) | c;

			result += base64Chars.charAt((bitmap >> 18) & 63);
			result += base64Chars.charAt((bitmap >> 12) & 63);
			result += i - 2 < str.length ? base64Chars.charAt((bitmap >> 6) & 63) : '=';
			result += i - 1 < str.length ? base64Chars.charAt(bitmap & 63) : '=';
		}
		return result;
	};

	// Add random noise to encoded string
	var addNoise = function(encodedStr) {
		var result = '';
		var noiseCount = Math.floor(Math.random() * 5) + 3; // 3-7 noise characters

		for (var i = 0; i < encodedStr.length; i++) {
			result += encodedStr.charAt(i);

			// Randomly insert noise characters
			if (Math.random() < 0.3) { // 30% chance to add noise after each character
				var randomNoise = noiseChars.charAt(Math.floor(Math.random() * noiseChars.length));
				result += randomNoise;
			}
		}

		// Add some noise at the end
		for (var j = 0; j < noiseCount; j++) {
			result += noiseChars.charAt(Math.floor(Math.random() * noiseChars.length));
		}

		return result;
	};

	// Remove noise from encoded string (for Lua decode function)
	var noiseRemovalFunctionName;
	var generateNoiseRemovalCode = function() {
		// Generate random function name
		noiseRemovalFunctionName = generateRandomIdentifier();

		// Generate random variable names
		var paramName = generateRandomIdentifier();
		var validVarName = generateRandomIdentifier();
		var resultVarName = generateRandomIdentifier();
		var loopVarName = generateRandomIdentifier();
		var charVarName = generateRandomIdentifier();

		// Create a string of valid base64 characters for Lua
		var validChars = base64Chars + '=';
		var luaValidChars = '';
		for (var i = 0; i < validChars.length; i++) {
			luaValidChars += validChars.charAt(i);
		}

		// Generate Lua code to remove noise characters with random names
		return 'local function ' + noiseRemovalFunctionName + '(' + paramName + ')' +
			'local ' + validVarName + '="' + luaValidChars + '"' +
			'local ' + resultVarName + '=""' +
			'for ' + loopVarName + '=1,#' + paramName + ' do ' +
			'local ' + charVarName + '=' + paramName + ':sub(' + loopVarName + ',' + loopVarName + ')' +
			'if ' + validVarName + ':find(' + charVarName + ',1,true)then ' + resultVarName + '=' + resultVarName + '..' + charVarName + ' end ' +
			'end return ' + resultVarName + ' end;';
	};

	// **DISPATCHER IMPLEMENTATION**
	// Creates a middleman function to process function calls
	var dispatcherFunctionName;
	var dispatchedFunctions = {}; // Maps original function names to dispatcher IDs
	var dispatcherCounter = 0;

	var createDispatcher = function() {
		dispatcherFunctionName = generateRandomIdentifier();

		// Reset dispatcher state
		dispatchedFunctions = {};
		dispatcherCounter = 0; // Start from 0, first function gets id 1

		return dispatcherFunctionName;
	};

	var registerFunctionForDispatch = function(functionName) {
		if (!dispatchedFunctions[functionName]) {
			dispatchedFunctions[functionName] = ++dispatcherCounter; // Pre-increment to start from 1
		}
		return dispatchedFunctions[functionName];
	};

	var generateDispatcherFunction = function() {
		if (Object.keys(dispatchedFunctions).length === 0) {
			return ''; // No functions to dispatch
		}

		var dispatcherCode = 'local function ' + dispatcherFunctionName + '(id,...)';
		
		// Build the dispatcher switch logic
		var hasConditions = false;
		for (var funcName in dispatchedFunctions) {
			var id = dispatchedFunctions[funcName];
			if (!hasConditions) {
				dispatcherCode += 'if id==' + id + ' then return ' + funcName + '(...)';
				hasConditions = true;
			} else {
				dispatcherCode += 'elseif id==' + id + ' then return ' + funcName + '(...)';
			}
		}
		
		if (hasConditions) {
			dispatcherCode += ' end';
		}
		
		dispatcherCode += ' end;';
		return dispatcherCode;
	};

	var shouldDispatchFunction = function(functionName) {
		// Dispatch common Lua functions to obfuscate calls
		var functionsToDispatch = [
			'print', 'type', 'pairs', 'ipairs', 'next', 'tonumber', 'tostring',
			'string.sub', 'string.len', 'string.find', 'string.match',
			'table.insert', 'table.remove', 'table.sort', 'table.concat',
			'math.floor', 'math.ceil', 'math.random', 'math.abs'
		];

		return functionsToDispatch.indexOf(functionName) > -1;
	};

	var each = function(array, fn) {
		var index = -1;
		var length = array.length;
		var max = length - 1;
		while (++index < length) {
			fn(array[index], index < max);
		}
	};

	var indexOf = function(array, value) {
		var index = -1;
		var length = array.length;
		while (++index < length) {
			if (array[index] == value) {
				return index;
			}
		}
	};

	var hasOwnProperty = {}.hasOwnProperty;
	var extend = function(destination, source) {
		var key;
		if (source) {
			for (key in source) {
				if (hasOwnProperty.call(source, key)) {
					destination[key] = source[key];
				}
			}
		}
		return destination;
	};

	var generateZeroes = function(length) {
		var zero = '0';
		var result = '';
		if (length < 1) {
			return result;
		}
		if (length == 1) {
			return zero;
		}
		while (length) {
			if (length & 1) {
				result += zero;
			}
			if (length >>= 1) {
				zero += zero;
			}
		}
		return result;
	};

	// http://www.lua.org/manual/5.2/manual.html#3.1
	function isKeyword(id) {
		switch (id.length) {
			case 2:
				return 'do' == id || 'if' == id || 'in' == id || 'or' == id;
			case 3:
				return 'and' == id || 'end' == id || 'for' == id || 'nil' == id ||
					'not' == id;
			case 4:
				return 'else' == id || 'goto' == id || 'then' == id || 'true' == id;
			case 5:
				return 'break' == id || 'false' == id || 'local' == id ||
					'until' == id || 'while' == id;
			case 6:
				return 'elseif' == id || 'repeat' == id || 'return' == id;
			case 8:
				return 'function' == id;
		}
		return false;
	}

	var identifierMap;
	var identifiersInUse;
	var base64DecodeFunctionName;
	var shouldEncodeStrings;
	var stringArray = []; // Array to store all strings
	var stringArrayName; // Name of the string array variable
	var decodeFunctionName; // Name of the decode function

	// Generate random identifier with 8-13 characters (a-z, A-Z)
	var generateRandomIdentifier = function() {
		var length = MIN_IDENTIFIER_LENGTH + Math.floor(Math.random() * (MAX_IDENTIFIER_LENGTH - MIN_IDENTIFIER_LENGTH + 1));
		var result = '';
		for (var i = 0; i < length; i++) {
			result += RANDOM_CHARS.charAt(Math.floor(Math.random() * RANDOM_CHARS.length));
		}
		return result;
	};

	var generateIdentifier = function(originalName) {
		// Preserve `self` in methods
		if (originalName == 'self') {
			return originalName;
		}

		if (hasOwnProperty.call(identifierMap, originalName)) {
			return identifierMap[originalName];
		}

		var newIdentifier;
		var attempts = 0;
		var maxAttempts = 1000; // Prevent infinite loops

		do {
			newIdentifier = generateRandomIdentifier();
			attempts++;
			if (attempts > maxAttempts) {
				throw new Error('Unable to generate unique identifier after ' + maxAttempts + ' attempts');
			}
		} while (
			isKeyword(newIdentifier) ||
			indexOf(identifiersInUse, newIdentifier) > -1
		);

		identifierMap[originalName] = newIdentifier;
		identifiersInUse.push(newIdentifier);
		return newIdentifier;
	};

	var shouldRenameIdentifier = function(expression) {
		// Always rename local identifiers
		if (expression.isLocal) {
			return true;
		}
		
		// For global identifiers, check if we have a mapping (meaning it's a user-defined function/variable)
		if (hasOwnProperty.call(identifierMap, expression.name)) {
			return true;
		}
		
		// Don't rename global identifiers that aren't in our map (built-in functions)
		return false;
	};

	/*--------------------------------------------------------------------------*/

	var joinStatements = function(a, b, separator) {
		separator || (separator = ' ');

		var lastCharA = a.slice(-1);
		var firstCharB = b.charAt(0);

		if (lastCharA == '' || firstCharB == '') {
			return a + b;
		}
		if (regexAlphaUnderscore.test(lastCharA)) {
			if (regexAlphaNumUnderscore.test(firstCharB)) {
				// e.g. `while` + `1`
				// e.g. `local a` + `local b`
				return a + separator + b;
			} else {
				// e.g. `not` + `(2>3 or 3<2)`
				// e.g. `x` + `^`
				return a + b;
			}
		}
		if (regexDigits.test(lastCharA)) {
			if (
				firstCharB == '(' ||
				!(firstCharB == '.' ||
				regexAlphaUnderscore.test(firstCharB))
			) {
				// e.g. `1` + `+`
				// e.g. `1` + `==`
				return a + b;
			} else {
				// e.g. `1` + `..`
				// e.g. `1` + `and`
				return a + separator + b;
			}
		}
		if (lastCharA == firstCharB && lastCharA == '-') {
			// e.g. `1-` + `-2`
			return a + separator + b;
		}
		return a + b;
	};

	var formatBase = function(base) {
		var result = '';
		var type = base.type;
		var needsParens = base.inParens && (
			type == 'BinaryExpression' ||
			type == 'FunctionDeclaration' ||
			type == 'TableConstructorExpression' ||
			type == 'LogicalExpression' ||
			type == 'StringLiteral'
		);
		if (needsParens) {
			result += '(';
		}
		result += formatExpression(base);
		if (needsParens) {
			result += ')';
		}
		return result;
	};

	var formatExpression = function(expression, options) {

		options = extend({
			'precedence': 0,
			'preserveIdentifiers': false
		}, options);

		var result = '';
		var currentPrecedence;
		var associativity;
		var operator;

		var expressionType = expression.type;

		if (expressionType == 'Identifier') {

			// Always rename identifiers unless explicitly preserving them
			result = shouldRenameIdentifier(expression) && !options.preserveIdentifiers
				? generateIdentifier(expression.name)
				: expression.name;

		} else if (expressionType == 'StringLiteral') {

			if (expression.raw.length > 4) { // Only encode non-trivial strings
				// Extract the string content without quotes
				var stringContent = expression.raw.slice(1, -1); // Remove surrounding quotes

				// Simple escape handling - handle most common escapes
				stringContent = stringContent.replace(/\\n/g, '\n')
											 .replace(/\\t/g, '\t')
											 .replace(/\\r/g, '\r')
											 .replace(/\\\\/g, '\\')
											 .replace(/\\"/g, '"')
											 .replace(/\\'/g, "'");

				// Encode with base64 and add noise
				var encoded = base64Encode(stringContent);
				var noisyEncoded = addNoise(encoded);

				// Add to string array and get index (Lua uses 1-based indexing)
				var stringIndex = stringArray.length + 1;
				stringArray.push(noisyEncoded);

				// Return reference to string array with decode function
				result = decodeFunctionName + '(' + stringArrayName + '[' + stringIndex + '])';
			} else {
				result = expression.raw;
			}

		} else if (
			expressionType == 'NumericLiteral' ||
			expressionType == 'BooleanLiteral' ||
			expressionType == 'NilLiteral' ||
			expressionType == 'VarargLiteral'
		) {

			result = expression.raw;

		} else if (
			expressionType == 'LogicalExpression' ||
			expressionType == 'BinaryExpression'
		) {

			// If an expression with precedence x
			// contains an expression with precedence < x,
			// the inner expression must be wrapped in parens.
			operator = expression.operator;
			currentPrecedence = PRECEDENCE[operator];
			associativity = 'left';

			result = formatExpression(expression.left, {
				'precedence': currentPrecedence,
				'direction': 'left',
				'parent': operator
			});
			result = joinStatements(result, operator);
			result = joinStatements(result, formatExpression(expression.right, {
				'precedence': currentPrecedence,
				'direction': 'right',
				'parent': operator
			}));

			if (operator == '^' || operator == '..') {
				associativity = "right";
			}

			if (
				currentPrecedence < options.precedence ||
				(
					currentPrecedence == options.precedence &&
					associativity != options.direction &&
					options.parent != '+' &&
					!(options.parent == '*' && (operator == '/' || operator == '*'))
				)
			) {
				// The most simple case here is that of
				// protecting the parentheses on the RHS of
				// `1 - (2 - 3)` but deleting them from `(1 - 2) - 3`.
				// This is generally the right thing to do. The
				// semantics of `+` are special however: `1 + (2 - 3)`
				// == `1 + 2 - 3`. `-` and `+` are the only two operators
				// who share their precedence level. `*` also can
				// commute in such a way with `/`, but not with `%`
				// (all three share a precedence). So we test for
				// all of these conditions and avoid emitting
				// parentheses in the cases where we don't have to.
				result = '(' + result + ')';
			}

		} else if (expressionType == 'UnaryExpression') {

			operator = expression.operator;
			currentPrecedence = PRECEDENCE['unary' + operator];

			result = joinStatements(
				operator,
				formatExpression(expression.argument, {
					'precedence': currentPrecedence
				})
			);

			if (
				currentPrecedence < options.precedence &&
				// In principle, we should parenthesize the RHS of an
				// expression like `3^-2`, because `^` has higher precedence
				// than unary `-` according to the manual. But that is
				// misleading on the RHS of `^`, since the parser will
				// always try to find a unary operator regardless of
				// precedence.
				!(
					(options.parent == '^') &&
					options.direction == 'right'
				)
			) {
				result = '(' + result + ')';
			}

		} else if (expressionType == 'CallExpression') {

			// **DISPATCHER INTEGRATION FOR FUNCTION CALLS**
			var baseResult = formatBase(expression.base);
			
			// Check if this is a function call that should be dispatched
			var shouldDispatch = false;
			var functionName = '';
			
			// Extract function name for simple identifiers
			if (expression.base.type === 'Identifier') {
				functionName = expression.base.name;
				shouldDispatch = shouldDispatchFunction(functionName);
			} else if (expression.base.type === 'MemberExpression') {
				// Handle member expressions like string.sub, table.insert
				var objectName = expression.base.base.name || '';
				var methodName = expression.base.identifier.name || '';
				functionName = objectName + '.' + methodName;
				shouldDispatch = shouldDispatchFunction(functionName);
			}
			
			if (shouldDispatch) {
				var dispatchId = registerFunctionForDispatch(functionName);
				result = dispatcherFunctionName + '(' + dispatchId + ',';
				
				each(expression.arguments, function(argument, needsComma) {
					result += formatExpression(argument);
					if (needsComma) {
						result += ',';
					}
				});
				result += ')';
			} else {
				// Normal function call formatting
				result = baseResult + '(';
				each(expression.arguments, function(argument, needsComma) {
					result += formatExpression(argument);
					if (needsComma) {
						result += ',';
					}
				});
				result += ')';
			}

		} else if (expressionType == 'TableCallExpression') {

			result = formatExpression(expression.base) +
				formatExpression(expression.arguments);

		} else if (expressionType == 'StringCallExpression') {

			result = formatExpression(expression.base) +
				formatExpression(expression.argument);

		} else if (expressionType == 'IndexExpression') {

			result = formatBase(expression.base) + '[' +
				formatExpression(expression.index) + ']';

		} else if (expressionType == 'MemberExpression') {

			result = formatBase(expression.base) + expression.indexer +
				formatExpression(expression.identifier, {
					'preserveIdentifiers': true
				});

		} else if (expressionType == 'FunctionDeclaration') {

			result = 'function(';
			if (expression.parameters.length) {
				each(expression.parameters, function(parameter, needsComma) {
					// `Identifier`s have a `name`, `VarargLiteral`s have a `value`
					result += parameter.name
						? generateIdentifier(parameter.name)
						: parameter.value;
					if (needsComma) {
						result += ',';
					}
				});
			}
			result += ')';
			result = joinStatements(result, formatStatementList(expression.body));
			result = joinStatements(result, 'end');

		} else if (expressionType == 'TableConstructorExpression') {

			result = '{';

			each(expression.fields, function(field, needsComma) {
				if (field.type == 'TableKey') {
					result += '[' + formatExpression(field.key) + ']=' +
						formatExpression(field.value);
				} else if (field.type == 'TableValue') {
					result += formatExpression(field.value);
				} else { // at this point, `field.type == 'TableKeyString'`
					result += formatExpression(field.key, {
						// TODO: keep track of nested scopes (#18)
						'preserveIdentifiers': true
					}) + '=' + formatExpression(field.value);
				}
				if (needsComma) {
					result += ',';
				}
			});

			result += '}';

		} else {

			throw TypeError('Unknown expression type: `' + expressionType + '`');

		}

		return result;
	};

	var formatStatementList = function(body) {
		var result = '';
		each(body, function(statement) {
			result = joinStatements(result, formatStatement(statement), ';');
		});
		return result;
	};

	var formatStatement = function(statement) {
		var result = '';
		var statementType = statement.type;

		if (statementType == 'AssignmentStatement') {

			// left-hand side
			each(statement.variables, function(variable, needsComma) {
				result += formatExpression(variable);
				if (needsComma) {
					result += ',';
				}
			});

			// right-hand side
			result += '=';
			each(statement.init, function(init, needsComma) {
				result += formatExpression(init);
				if (needsComma) {
					result += ',';
				}
			});

		} else if (statementType == 'LocalStatement') {

			result = 'local ';

			// left-hand side
			each(statement.variables, function(variable, needsComma) {
				// Variables in a `LocalStatement` are always local, duh
				result += generateIdentifier(variable.name);
				if (needsComma) {
					result += ',';
				}
			});

			// right-hand side
			if (statement.init.length) {
				result += '=';
				each(statement.init, function(init, needsComma) {
					result += formatExpression(init);
					if (needsComma) {
						result += ',';
					}
				});
			}

		} else if (statementType == 'CallStatement') {

			result = formatExpression(statement.expression);

		} else if (statementType == 'IfStatement') {

			result = joinStatements(
				'if',
				formatExpression(statement.clauses[0].condition)
			);
			result = joinStatements(result, 'then');
			result = joinStatements(
				result,
				formatStatementList(statement.clauses[0].body)
			);
			each(statement.clauses.slice(1), function(clause) {
				if (clause.condition) {
					result = joinStatements(result, 'elseif');
					result = joinStatements(result, formatExpression(clause.condition));
					result = joinStatements(result, 'then');
				} else {
					result = joinStatements(result, 'else');
				}
				result = joinStatements(result, formatStatementList(clause.body));
			});
			result = joinStatements(result, 'end');

		} else if (statementType == 'WhileStatement') {

			result = joinStatements('while', formatExpression(statement.condition));
			result = joinStatements(result, 'do');
			result = joinStatements(result, formatStatementList(statement.body));
			result = joinStatements(result, 'end');

		} else if (statementType == 'DoStatement') {

			result = joinStatements('do', formatStatementList(statement.body));
			result = joinStatements(result, 'end');

		} else if (statementType == 'ReturnStatement') {

			result = 'return';

			each(statement.arguments, function(argument, needsComma) {
				result = joinStatements(result, formatExpression(argument));
				if (needsComma) {
					result += ',';
				}
			});

		} else if (statementType == 'BreakStatement') {

			result = 'break';

		} else if (statementType == 'RepeatStatement') {

			result = joinStatements('repeat', formatStatementList(statement.body));
			result = joinStatements(result, 'until');
			result = joinStatements(result, formatExpression(statement.condition))

		} else if (statementType == 'FunctionDeclaration') {

			result = (statement.isLocal ? 'local ' : '') + 'function ';
			
			// Always rename function names (both local and global user-defined functions)
			result += generateIdentifier(statement.identifier.name);
			result += '(';

			if (statement.parameters.length) {
				each(statement.parameters, function(parameter, needsComma) {
					// `Identifier`s have a `name`, `VarargLiteral`s have a `value`
					result += parameter.name
						? generateIdentifier(parameter.name)
						: parameter.value;
					if (needsComma) {
						result += ',';
					}
				});
			}

			result += ')';
			result = joinStatements(result, formatStatementList(statement.body));
			result = joinStatements(result, 'end');

		} else if (statementType == 'ForGenericStatement') {
			// see also `ForNumericStatement`

			result = 'for ';

			each(statement.variables, function(variable, needsComma) {
				// The variables in a `ForGenericStatement` are always local
				result += generateIdentifier(variable.name);
				if (needsComma) {
					result += ',';
				}
			});

			result += ' in';

			each(statement.iterators, function(iterator, needsComma) {
				result = joinStatements(result, formatExpression(iterator));
				if (needsComma) {
					result += ',';
				}
			});

			result = joinStatements(result, 'do');
			result = joinStatements(result, formatStatementList(statement.body));
			result = joinStatements(result, 'end');

		} else if (statementType == 'ForNumericStatement') {

			// The variables in a `ForNumericStatement` are always local
			result = 'for ' + generateIdentifier(statement.variable.name) + '=';
			result += formatExpression(statement.start) + ',' +
				formatExpression(statement.end);

			if (statement.step) {
				result += ',' + formatExpression(statement.step);
			}

			result = joinStatements(result, 'do');
			result = joinStatements(result, formatStatementList(statement.body));
			result = joinStatements(result, 'end');

		} else if (statementType == 'LabelStatement') {

			// Labels are not supported in Lua 5.1 - skip or throw error
			throw TypeError('LabelStatement not supported in Lua 5.1');

		} else if (statementType == 'GotoStatement') {

			// Goto is not supported in Lua 5.1 - skip or throw error
			throw TypeError('GotoStatement not supported in Lua 5.1');

		} else {

			throw TypeError('Unknown statement type: `' + statementType + '`');

		}

		return result;
	};

	var minify = function(argument, options) {
		// `argument` can be a Lua code snippet (string)
		// or a luaparse-compatible AST (object)
		// `options` can specify additional settings
		options = options || {};

		var ast = typeof argument == 'string'
			? parse(argument)
			: argument;

		// (Re)set temporary identifier values
		identifierMap = {};
		identifiersInUse = [];
		shouldEncodeStrings = true; // Always enable string encoding
		stringArray = []; // Reset string array

		// Generate function names
		base64DecodeFunctionName = generateIdentifier('base64_decode');
		stringArrayName = generateIdentifier('strings');
		decodeFunctionName = generateIdentifier('decode');

		// Initialize dispatcher
		createDispatcher();

		// Don't preserve any global variable names - rename everything
		if (ast.globals) {
			// All globals will be renamed since we removed the preservation logic
		} else {
			throw Error('Missing required AST property: `globals`');
		}

		var result = formatStatementList(ast.body);

		// Generate string array if we have strings
		var stringArrayCode = '';
		if (stringArray.length > 0) {
			stringArrayCode = 'local ' + stringArrayName + '={';
			for (var i = 0; i < stringArray.length; i++) {
				stringArrayCode += '"' + stringArray[i] + '"';
				if (i < stringArray.length - 1) {
					stringArrayCode += ',';
				}
			}
			stringArrayCode += '};';
		}

		// Generate noise removal function
		var noiseRemovalCode = generateNoiseRemovalCode();

		// Generate base64 decode function with random variable names (Lua 5.1 compatible)
		var generateBase64DecodeFunction = function() {
			// Generate random variable names
			var paramName = generateRandomIdentifier();
			var base64CharsVar = generateRandomIdentifier();
			var tableVar = generateRandomIdentifier();
			var loopVar1 = generateRandomIdentifier();
			var resultVar = generateRandomIdentifier();
			var loopVar2 = generateRandomIdentifier();
			var var1 = generateRandomIdentifier();
			var var2 = generateRandomIdentifier();
			var var3 = generateRandomIdentifier();
			var var4 = generateRandomIdentifier();
			var tempVar = generateRandomIdentifier();

			// Lua 5.1 compatible bitwise operations using math.floor and math.fmod
			return 'local function ' + base64DecodeFunctionName + '(' + paramName + ')' +
				'local ' + base64CharsVar + '="' + base64Chars + '"' +
				'local ' + tableVar + '={}for ' + loopVar1 + '=1,#' + base64CharsVar + ' do ' + tableVar + '[' + base64CharsVar + ':sub(' + loopVar1 + ',' + loopVar1 + ')]=' + loopVar1 + '-1 end ' +
				'local ' + resultVar + '=""' +
				'for ' + loopVar2 + '=1,#' + paramName + ',4 do ' +
				'local ' + var1 + ',' + var2 + ',' + var3 + ',' + var4 + '=' + tableVar + '[' + paramName + ':sub(' + loopVar2 + ',' + loopVar2 + ')],' + tableVar + '[' + paramName + ':sub(' + loopVar2 + '+1,' + loopVar2 + '+1)],' + tableVar + '[' + paramName + ':sub(' + loopVar2 + '+2,' + loopVar2 + '+2)],' + tableVar + '[' + paramName + ':sub(' + loopVar2 + '+3,' + loopVar2 + '+3)]' +
				'if ' + var1 + ' and ' + var2 + ' then ' +
				'local ' + tempVar + '=' + var1 + '*4+math.floor(' + var2 + '/16)' +
				resultVar + '=' + resultVar + '..string.char(' + tempVar + ')' +
				'if ' + var3 + ' then ' + tempVar + '=math.fmod(' + var2 + ',16)*16+math.floor(' + var3 + '/4)' + resultVar + '=' + resultVar + '..string.char(' + tempVar + ')end ' +
				'if ' + var4 + ' then ' + tempVar + '=math.fmod(' + var3 + ',4)*64+' + var4 + ' ' + resultVar + '=' + resultVar + '..string.char(' + tempVar + ')end ' +
				'end end return ' + resultVar + ' end;';
		};

		// Prepend base64 decode function - FIXED VERSION
		var decodeFunction = generateBase64DecodeFunction();

		// Generate decode function that removes noise and decodes
		var mainDecodeFunction = 'local function ' + decodeFunctionName + '(s)' +
			'return ' + base64DecodeFunctionName + '(' + noiseRemovalFunctionName + '(s))end;';

		// Generate and prepend dispatcher function
		var dispatcherCode = generateDispatcherFunction();

		result = stringArrayCode + noiseRemovalCode + decodeFunction + mainDecodeFunction + dispatcherCode + result;

		return result;
	};

	/*--------------------------------------------------------------------------*/

	var luamin = {
		'version': '1.0.4-base64-dispatcher-lua51',
		'minify': minify
	};

	// Some AMD build optimizers, like r.js, check for specific condition patterns
	// like the following:
	if (
		typeof define == 'function' &&
		typeof define.amd == 'object' &&
		define.amd
	) {
		define(function() {
			return luamin;
		});
	}	else if (freeExports && !freeExports.nodeType) {
		if (freeModule) { // in Node.js or RingoJS v0.8.0+
			freeModule.exports = luamin;
		} else { // in Narwhal or RingoJS v0.7.0-
			extend(freeExports, luamin);
		}
	} else { // in Rhino or a web browser
		root.luamin = luamin;
	}

}(this));