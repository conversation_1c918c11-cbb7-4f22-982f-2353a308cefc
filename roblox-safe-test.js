const luamin = require('./luamin.js');

// Test with Roblox-specific safety wrapper
const robloxCodeWithSafety = `
-- Roblox script with safety checks
local function waitForService(serviceName)
    local success, service = pcall(function()
        return game:GetService(serviceName)
    end)
    if success then
        return service
    else
        warn("Failed to get service: " .. serviceName)
        return nil
    end
end

local function safeGetCurrentCamera()
    if not workspace then
        warn("Workspace is nil")
        return nil
    end
    
    local camera = workspace.CurrentCamera
    if not camera then
        warn("CurrentCamera is nil")
        return nil
    end
    
    return camera
end

-- Main script with safety checks
local Players = waitForService("Players")
if not Players then
    error("Players service not available")
    return
end

local player = Players.LocalPlayer
if not player then
    warn("LocalPlayer not available")
    return
end

-- Wait for character with timeout
local character = player.Character
local attempts = 0
while not character and attempts < 50 do
    wait(0.1)
    character = player.Character
    attempts = attempts + 1
end

if not character then
    warn("Character failed to load after 5 seconds")
    return
end

-- Safe camera access
local camera = safeGetCurrentCamera()
if camera then
    print("Camera found successfully: " .. tostring(camera))
    
    -- Safe property access
    local success, result = pcall(function()
        camera.CameraType = Enum.CameraType.Custom
        return true
    end)
    
    if success then
        print("Camera configured successfully")
    else
        warn("Failed to configure camera: " .. tostring(result))
    end
else
    warn("Could not access CurrentCamera")
end

-- Example of safe table operations
local inventory = {
    coins = 100,
    items = {"Sword", "Shield", "Potion"}
}

for i, item in ipairs(inventory.items) do
    print("Item " .. i .. ": " .. item)
end

print("Script completed successfully")
`;

console.log('Testing Roblox-safe obfuscated code...\n');
console.log('Original code with safety checks:');
console.log(robloxCodeWithSafety);
console.log('\n' + '='.repeat(100) + '\n');

try {
    const obfuscated = luamin.minify(robloxCodeWithSafety);
    console.log('Obfuscated Roblox-safe code:');
    console.log(obfuscated);
    
    // Analysis
    console.log('\n' + '='.repeat(100));
    console.log('SAFETY ANALYSIS:');
    console.log('='.repeat(100));
    
    // Check for safety patterns
    if (obfuscated.includes('pcall')) {
        console.log('✓ pcall safety checks preserved');
    }
    
    if (obfuscated.includes('workspace.CurrentCamera')) {
        console.log('✓ Direct workspace.CurrentCamera access preserved');
    }
    
    // Check dispatcher calls
    const dispatcherMatches = obfuscated.match(/\w+\(\d+,/g);
    if (dispatcherMatches) {
        console.log('✓ Dispatcher calls found:');
        const uniqueCalls = [...new Set(dispatcherMatches)];
        uniqueCalls.forEach(match => {
            console.log('  ' + match);
        });
    }
    
    console.log('\n📋 RECOMMENDATIONS FOR ROBLOX:');
    console.log('1. Use this obfuscated code in a LocalScript (not ServerScript)');
    console.log('2. Place the script in StarterPlayerScripts or StarterGui');
    console.log('3. The safety checks should prevent nil access errors');
    console.log('4. If errors persist, add more wait() calls before accessing workspace');
    
} catch (error) {
    console.error('❌ ERROR during obfuscation:', error.message);
    console.error('Stack trace:', error.stack);
}
