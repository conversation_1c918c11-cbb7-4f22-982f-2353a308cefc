const express = require('express');
const cors = require('cors');
const path = require('path');
const luamin = require('./luamin');
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Obfuscation utility functions
class LuaObfuscator {
    // Wrap code with Roblox safety checks
    wrapWithRobloxSafety(luaCode) {
        const safetyWrapper = `
-- Roblox Safety Wrapper (Auto-generated)
local RunService = game:GetService("RunService")
local Players = game:GetService("Players")

-- Safe workspace checker
local function waitForWorkspace()
    local attempts = 0
    while not workspace and attempts < 100 do
        if RunService and RunService.Heartbeat then
            RunService.Heartbeat:Wait()
        else
            wait(0.1)
        end
        attempts = attempts + 1
    end
    return workspace ~= nil
end

-- Ultra-safe CurrentCamera getter
local function getSafeCurrentCamera()
    -- Ensure workspace exists first
    if not waitForWorkspace() then
        warn("Workspace failed to load")
        return nil
    end

    -- Method 1: Direct check
    if workspace.CurrentCamera then
        return workspace.CurrentCamera
    end

    -- Method 2: Wait with heartbeat
    local attempts = 0
    while attempts < 100 do
        if workspace.CurrentCamera then
            return workspace.CurrentCamera
        end
        if RunService and RunService.Heartbeat then
            RunService.Heartbeat:Wait()
        else
            wait(0.05)
        end
        attempts = attempts + 1
    end

    -- Method 3: Try FindFirstChild
    local success, camera = pcall(function()
        return workspace:FindFirstChild("Camera") or workspace:FindFirstChild("CurrentCamera")
    end)
    if success and camera then
        return camera
    end

    -- Method 4: Force wait and retry
    wait(2)
    return workspace.CurrentCamera
end

-- Replace all workspace.CurrentCamera references
local OriginalWorkspace = workspace
local SafeWorkspace = setmetatable({}, {
    __index = function(t, k)
        if k == "CurrentCamera" then
            return getSafeCurrentCamera()
        else
            return OriginalWorkspace[k]
        end
    end,
    __newindex = function(t, k, v)
        OriginalWorkspace[k] = v
    end
})

-- Override workspace reference
workspace = SafeWorkspace

-- Execute original code safely
local function executeOriginalCode()
${luaCode.split('\n').map(line => '    ' + line).join('\n')}
end

-- Run with error handling
local success, errorMsg = pcall(executeOriginalCode)
if not success then
    warn("Script execution failed: " .. tostring(errorMsg))
end
`;
        return safetyWrapper;
    }

    // Main obfuscation function with enhanced complexity
    obfuscate(luaCode, options = {}) {
        try {
            // Add Roblox safety wrapper if requested
            let processedCode = luaCode;
            if (options.robloxSafe) {
                processedCode = this.wrapWithRobloxSafety(luaCode);
            }

            // Use the enhanced luamin with all obfuscation features
            let obfuscatedCode = luamin.minify(processedCode);

            return {
                success: true,
                obfuscated: obfuscatedCode,
                originalSize: luaCode.length,
                obfuscatedSize: obfuscatedCode.length,
                features: [
                    'Base64 String Encoding',
                    'XOR Encryption',
                    'Virtual Machine Dispatcher',
                    'Random Variable Names',
                    'Lua 5.1 Compatible',
                    options.robloxSafe ? 'Roblox CurrentCamera Safety' : null
                ].filter(f => f !== null)
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Create obfuscator instance
const obfuscator = new LuaObfuscator();

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.post('/api/obfuscate', (req, res) => {
    const { code, options = {} } = req.body;

    if (!code || typeof code !== 'string') {
        return res.status(400).json({
            success: false,
            error: 'Invalid Lua code provided'
        });
    }

    const result = obfuscator.obfuscate(code, options);
    res.json(result);
});

// Special endpoint for Roblox-safe obfuscation
app.post('/api/obfuscate-roblox', (req, res) => {
    const { code } = req.body;

    if (!code || typeof code !== 'string') {
        return res.status(400).json({
            success: false,
            error: 'Invalid Lua code provided'
        });
    }

    const result = obfuscator.obfuscate(code, { robloxSafe: true });
    res.json(result);
});

// Start server
app.listen(PORT, () => {
    console.log(`Lua Obfuscator server running on http://localhost:${PORT}`);
});

module.exports = app;